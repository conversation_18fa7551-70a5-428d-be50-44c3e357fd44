﻿namespace PSSmartMemo.Services;

public class TemplateDataService(IDbContextFactory<ApplicationDbContext> contextFactory) : IDataService<TemplateDto>
{
    public Task<string> DeleteById(int id, string user)
    {
        var dc = contextFactory.CreateDbContext();
        var q = dc.MemoTemplates.Find(id);
        string msg;
        if (q != null)
            try
            {
                //dc.MemoTemplates.Remove(q);
                q.MemoTemplateIsDel = true;
                q.MemoTemplateModifiedBy = user;
                q.MemoTemplateModifiedDate = DateTime.Now;
                dc.SaveChanges();
                msg = "OK";
            }
            catch (Exception ex)
            {
                msg = ex.Message;
                if (ex.InnerException != null)
                    msg += ex.InnerException.Message;
            }
        else
            msg = "Record not found";

        return Task.FromResult(msg);
    }

    public Task<List<TemplateDto>> GetAll()
    {
        var dc = contextFactory.CreateDbContext();
        var q = (from a in dc.MemoTemplates
            orderby a.MemoTemplateTitle
            where a.MemoTemplateIsDel == false
            select new TemplateDto
            {
                IsActive = a.MemoTemplateIsActive,
                TemplateTitle = a.MemoTemplateTitle,
                TemplateId = a.MemoTemplateId,
                TemplateTypeId = a.MemoTemplateId,
                MemoTypeId = a.MemoTypeId,
                MemoTemplateCode = a.MemoTemplateCode,
                MemoTemplateVersion = a.MemoTemplateVersion,
                MemoTemplateAttachmentAllowed = a.MemoTemplateAttachmentAllowed,
                MemoTemplateApproverCountAllowed = a.MemoTemplateApproverCountAllowed,
                MemoTemplatePrefixCode = a.MemoTemplatePrefixCode,
                MemoTemplateAttachmentFileCountAllowed = (int)(a.MemoTemplateAttachmentFileCountAllowed ?? 0),
                MemoType = a.MemoType.MemoTypeName,
                MemoTemplateAttachmentPerFileSizeMbAllowed = a.MemoTemplateAttachmentPerFileSizeMballowed,
                TemplateStatus = a.MemoTemplateStatus == 0 ? "Draft" :
                    a.MemoTemplateStatus == 1 ? "Draft" :
                    a.MemoTemplateStatus == 2 ? "Published" :
                    a.MemoTemplateStatus == 3 ? "Archived" : "Error",
                TemplateStatusId = a.MemoTemplateStatus,
                CreatedDate = a.MemoTemplateCreatedDate,
                ModifiedDate = a.MemoTemplateModifiedDate
            }).ToList();
        return Task.FromResult(q);
    }

    public Task<TemplateDto?> GetById(int id)
    {
        var dc = contextFactory.CreateDbContext();
        var q = (from a in dc.MemoTemplates
            where a.MemoTemplateId == id
                  && a.MemoTemplateIsDel == false
            select new TemplateDto
            {
                IsActive = a.MemoTemplateIsActive,
                TemplateTitle = a.MemoTemplateTitle,
                TemplateId = a.MemoTemplateId,
                TemplateTypeId = a.MemoTemplateId,
                MemoTypeId = a.MemoTypeId,
                MemoTemplateCode = a.MemoTemplateCode,
                MemoTemplateVersion = a.MemoTemplateVersion,
                MemoTemplateAttachmentAllowed = a.MemoTemplateAttachmentAllowed,
                MemoTemplateApproverCountAllowed = a.MemoTemplateApproverCountAllowed,
                MemoTemplatePrefixCode = a.MemoTemplatePrefixCode,
                MemoTemplateAttachmentFileCountAllowed = (int)(a.MemoTemplateAttachmentFileCountAllowed ?? 0),
                MemoType = a.MemoType.MemoTypeName,
                MemoTemplateAttachmentPerFileSizeMbAllowed = a.MemoTemplateAttachmentPerFileSizeMballowed,
                TemplateStatus = a.MemoTemplateStatus == 0 ? "Draft" :
                    a.MemoTemplateStatus == 1 ? "Draft" :
                    a.MemoTemplateStatus == 2 ? "Published" :
                    a.MemoTemplateStatus == 3 ? "Archived" : "Error",
                TemplateStatusId = a.MemoTemplateStatus,
                Sections = (from b in dc.MemoTemplateSections
                    where b.MemoTemplateId == a.MemoTemplateId
                    orderby b.MemoTemplateSectionSortOrder
                    select new TemplateSectionDto
                    {
                        ContentHtml = b.MemoTemplateSectionContentHtml,
                        IsRequired = b.MemoTemplateSectionIsRequired,
                        SectionTitle = b.MemoTemplateSectionTitle,
                        TemplateSectionId = b.MemoTemplateSectionId,
                        SectionSortOrder = b.MemoTemplateSectionSortOrder,
                        IsWaterMark = b.MemoTemplateSectionIsWaterMark
                    }).ToList(),
                Approvers = (from ap in a.MemoTemplateApprovers
                    orderby ap.MemoTemplateApproverSortOrder
                    select new MemoTemplateApproverDto
                    {
                        AllowType = ap.MemoTemplateApproverAllowType,
                        ApproverRoleTitle = ap.MemoTemplateApproverTitle,
                        Id = ap.MemoTemplateApproverId,
                        MemoApproverRoleId = ap.MemoApproverRoleId
                    }).ToList()
            }).FirstOrDefault();
        return Task.FromResult(q);
    }

    public Task<(TemplateDto, string)> Save(TemplateDto entity, string user)
    {
        var dc = contextFactory.CreateDbContext();
        entity.TemplateTitle = (entity.TemplateTitle ?? "").Trim();

        var isTemplateExist = (from a in dc.MemoTemplates
            where a.MemoTemplateId != entity.TemplateId &&
                  a.MemoTemplateTitle == entity.TemplateTitle
            select a).Any();
        if (isTemplateExist) return Task.FromResult((entity, "Template already exist"));
        if (entity.TemplateId == 0)
        {
            var tmp = SaveNewMemoTemplate(entity, user);
            SaveTemplateSections(entity, user, tmp);
            SaveTemplateApprovers(entity, user, tmp);
            return Task.FromResult((entity, "OK"));
        }
        else
        {
            var tmp = dc.MemoTemplates.FirstOrDefault(c => c.MemoTemplateId == entity.TemplateId);
            if (tmp != null)
            {
                // if template not in draft mode then return database template object
                if (!(tmp.MemoTemplateStatus is 0 or 1))
                    return Task.FromResult((entity, "Template is not in draft mode"));
                UpdateTemplate(entity, user, tmp);
                SaveTemplateSections(entity, user, tmp);
                SaveTemplateApprovers(entity, user, tmp);

                return Task.FromResult((entity, "OK"));
            }

            return Task.FromResult((entity, "Record not found"));
        }
    }

    private void UpdateTemplateSections(TemplateDto entity, string user, MemoTemplate tmp)
    {
        var dc = contextFactory.CreateDbContext();
        var q =
            (from a in dc.MemoTemplateSections where a.MemoTemplateId == entity.TemplateId select a).ToList();
        foreach (var sec in q) sec.MemoTemplateSectionIsDel = true;
        dc.SaveChanges();
        // loop all items if found by TemplateSectionId then update else add new
        var idx = 0;
        foreach (var sec in entity.Sections)
        {
            var tmpSec =
                dc.MemoTemplateSections.FirstOrDefault(c => c.MemoTemplateSectionId == sec.TemplateSectionId);
            if (tmpSec == null)
            {
                tmpSec = new MemoTemplateSection
                {
                    MemoTemplateSectionId = sec.TemplateSectionId,
                    MemoTemplateId = tmp.MemoTemplateId,
                    MemoTemplateSectionTitle = sec.SectionTitle,
                    MemoTemplateSectionSortOrder = idx,
                    MemoTemplateSectionIsRequired = sec.IsRequired,
                    MemoTemplateSectionIsActive = true,
                    MemoTemplateSectionCreatedBy = user,
                    MemoTemplateSectionCreatedDate = DateTime.Now,
                    MemoTemplateSectionModifiedBy = user,
                    MemoTemplateSectionModifiedDate = DateTime.Now,
                    MemoTemplateSectionIsDel = false,
                    MemoTemplateSectionContentHtml = sec.ContentHtml
                };
                dc.MemoTemplateSections.Add(tmpSec);
            }
            else
            {
                tmpSec.MemoTemplateSectionTitle = sec.SectionTitle;
                tmpSec.MemoTemplateSectionSortOrder = idx;
                tmpSec.MemoTemplateSectionIsRequired = sec.IsRequired;
                tmpSec.MemoTemplateSectionIsActive = true;
                tmpSec.MemoTemplateSectionCreatedBy = user;
                tmpSec.MemoTemplateSectionCreatedDate = DateTime.Now;
                tmpSec.MemoTemplateSectionModifiedBy = user;
                tmpSec.MemoTemplateSectionModifiedDate = DateTime.Now;
                tmpSec.MemoTemplateSectionIsDel = false;
                tmpSec.MemoTemplateSectionContentHtml = sec.ContentHtml;
            }

            dc.SaveChanges();
            idx++;
        }
    }

    private void UpdateTemplate(TemplateDto entity, string user, MemoTemplate tmp)
    {
        var dc = contextFactory.CreateDbContext();
        tmp.MemoTemplateId = entity.TemplateId;


        tmp.MemoTemplateIsActive = entity.IsActive;
        tmp.MemoTemplateModifiedBy = user;
        tmp.MemoTemplateModifiedDate = DateTime.Now;
        tmp.MemoTemplateTitle = entity.TemplateTitle;
        tmp.MemoTypeId = entity.MemoTypeId;
        tmp.MemoTemplateCode = entity.MemoTemplateCode;
        tmp.MemoTemplateVersion = entity.MemoTemplateVersion;
        tmp.MemoTemplateAttachmentAllowed = entity.MemoTemplateAttachmentAllowed;
        tmp.MemoTemplateApproverCountAllowed = entity.MemoTemplateApproverCountAllowed;
        tmp.MemoTemplatePrefixCode = entity.MemoTemplatePrefixCode;
        tmp.MemoTemplateAttachmentFileCountAllowed = entity.MemoTemplateAttachmentFileCountAllowed;
        tmp.MemoTemplateAttachmentPerFileSizeMballowed = entity.MemoTemplateAttachmentPerFileSizeMbAllowed;
        dc.SaveChanges();
    }

    private void SaveTemplateApprovers(TemplateDto entity, string user, MemoTemplate tmp)
    {
        var dc = contextFactory.CreateDbContext();
        var aprs = dc.MemoTemplateApprovers.Where(c => c.MemoTemplateId == entity.TemplateId).ToList();
        dc.MemoTemplateApprovers.RemoveRange(aprs);
        dc.SaveChanges();
        var idx = 1;
        foreach (var ap in entity.Approvers)
        {
            var tmpApp = new MemoTemplateApprover
            {
                MemoTemplateId = tmp.MemoTemplateId,
                MemoTemplateApproverAllowType = ap.AllowType,
                MemoTemplateApproverTitle = ap.ApproverRoleTitle,
                MemoTemplateApproverSortOrder = idx,
                MemoTemplateApproverIsActive = true,
                MemoTemplateApproverCreatedBy = user,
                MemoTemplateApproverCreatedDate = DateTime.Now,
                MemoTemplateApproverModifiedBy = user,
                MemoTemplateApproverModifiedDate = DateTime.Now,
                MemoTemplateApproverIsDel = false,
                MemoApproverRoleId = ap.MemoApproverRoleId
            };
            dc.MemoTemplateApprovers.Add(tmpApp);
            dc.SaveChanges();
            idx++;
        }
    }

    private void SaveTemplateSections(TemplateDto entity, string user, MemoTemplate tmp)
    {
        var dc = contextFactory.CreateDbContext();
        var idx = 0;
        // mark is deleted all sections
        var ss = dc.MemoTemplateSections.Where(c => c.MemoTemplateId == entity.TemplateTypeId).ToList();
        foreach (var s in ss)
        {
            s.MemoTemplateSectionIsDel = true;
            s.MemoTemplateSectionModifiedBy = user;
            s.MemoTemplateSectionModifiedDate = DateTime.Now;
        }

        dc.SaveChanges();

        foreach (var sec in entity.Sections)
        {
            var tmpSec2 = dc.MemoTemplateSections.FirstOrDefault(cc => cc.MemoTemplateSectionId == sec.TemplateSectionId);
            if (tmpSec2 == null)
            {
                tmpSec2 = new MemoTemplateSection
                {
                    MemoTemplateSectionId = sec.TemplateSectionId,
                    MemoTemplateId = tmp.MemoTemplateId,
                    MemoTemplateSectionTitle = sec.SectionTitle,
                    MemoTemplateSectionContentHtml = sec.ContentHtml,
                    MemoTemplateSectionSortOrder = idx,
                    MemoTemplateSectionIsRequired = sec.IsRequired,
                    MemoTemplateSectionIsWaterMark = sec.IsWaterMark,
                    MemoTemplateSectionIsActive = true,
                    MemoTemplateSectionCreatedBy = user,
                    MemoTemplateSectionCreatedDate = DateTime.Now,
                    MemoTemplateSectionModifiedBy = user,
                    MemoTemplateSectionModifiedDate = DateTime.Now,
                    MemoTemplateSectionIsDel = false
                };
                dc.MemoTemplateSections.Add(tmpSec2);
                dc.SaveChanges();
            }
            else
            {
                tmpSec2.MemoTemplateSectionId = sec.TemplateSectionId;
                tmpSec2.MemoTemplateId = tmp.MemoTemplateId;
                tmpSec2.MemoTemplateSectionTitle = sec.SectionTitle;
                tmpSec2.MemoTemplateSectionContentHtml = sec.ContentHtml;
                tmpSec2.MemoTemplateSectionSortOrder = idx;
                tmpSec2.MemoTemplateSectionIsRequired = sec.IsRequired;
                tmpSec2.MemoTemplateSectionIsWaterMark = sec.IsWaterMark;
                tmpSec2.MemoTemplateSectionIsActive = true;
                tmpSec2.MemoTemplateSectionModifiedBy = user;
                tmpSec2.MemoTemplateSectionModifiedDate = DateTime.Now;
                tmpSec2.MemoTemplateSectionIsDel = false;
                dc.SaveChanges();
            }

            idx++;
        }
    }

    private MemoTemplate SaveNewMemoTemplate(TemplateDto entity, string user)
    {
        var dc = contextFactory.CreateDbContext();
        var tmp = new MemoTemplate
        {
            MemoTemplateCreatedBy = user,
            MemoTemplateCreatedDate = DateTime.Now,
            MemoTemplateIsActive = entity.IsActive,
            MemoTemplateModifiedBy = user,
            MemoTemplateModifiedDate = DateTime.Now,
            MemoTemplateTitle = entity.TemplateTitle,
            MemoTypeId = entity.MemoTypeId,

            MemoTemplateCode = entity.MemoTemplateCode,
            MemoTemplateVersion = entity.MemoTemplateVersion,
            MemoTemplateIsDel = false,
            MemoTemplateAttachmentAllowed = entity.MemoTemplateAttachmentAllowed,
            MemoTemplateApproverCountAllowed = entity.MemoTemplateApproverCountAllowed,
            MemoTemplatePrefixCode = entity.MemoTemplatePrefixCode,
            MemoTemplateAttachmentFileCountAllowed = entity.MemoTemplateAttachmentFileCountAllowed,
            MemoTemplateAttachmentPerFileSizeMballowed = entity.MemoTemplateAttachmentPerFileSizeMbAllowed
        };
        dc.MemoTemplates.Add(tmp);
        dc.SaveChanges();
        entity.TemplateId = tmp.MemoTemplateId;
        return tmp;
    }

    public Task<List<ApproverRoleDto>> GetAllMemoApproverRoles()
    {
        var dc = contextFactory.CreateDbContext();
        var q = (from a in dc.MemoApproverRoles
            orderby a.MemoApproverRoleType, a.MemoApproverRoleTitle
            where a.MemoApproverRoleIsActive &&
                  a.MemoApproverRoleIsDel == false &&
                  a.MemoApproverRoleTitle != "Initiator"
            select new ApproverRoleDto
            {
                Id = a.MemoApproverRoleId,
                Title = a.MemoApproverRoleTitle
            }).ToList();
        return Task.FromResult(q);
    }

    public Task<List<MemoTypeDto>> GetMemoTypes()
    {
        var dc = contextFactory.CreateDbContext();
        var q = (from a in dc.MemoTypes
            orderby a.MemoTypeName
            select new MemoTypeDto
            {
                Id = a.MemoTypeId,
                Title = a.MemoTypeName + (a.MemoTypeIsActive == true ? "" : " (Inactive)")
            }).ToList();
        return Task.FromResult(q);
    }

    public Task<List<FormTypeDto>> GetFormTypes()
    {
        var dc = contextFactory.CreateDbContext();
        var q = (from a in dc.FormTypes
            orderby a.FormTypeTitle
            select new FormTypeDto
            {
                Id = a.FormTypeId,
                Title = a.FormTypeTitle
            }).ToList();
        return Task.FromResult(q);
    }

    public Task<string> PublishTemplate(int templateDtoTemplateId, string userId)
    {
        var dc = contextFactory.CreateDbContext();
        var q = dc.MemoTemplates.Find(templateDtoTemplateId);
        if (q == null)
            return Task.FromResult("Record not found");
        q.MemoTemplateStatus = 2;
        q.MemoTemplateModifiedBy = userId;
        q.MemoTemplateModifiedDate = DateTime.Now;
        dc.SaveChanges();
        return Task.FromResult("OK");
    }

    public Task<List<RoleDTO>> GetAllRolesAsyncWithSelectedTemplate(int templateTemplateId)
    {
        var dc = contextFactory.CreateDbContext();
        var r = (from a in dc.Roles
            where a.IsActive
            orderby a.Name
            select new RoleDTO
            {
                Id = a.Id,
                Code = a.Code,
                Name = a.Name,
                isSelect = false
            }).ToList();
        foreach (var mr in r)
            mr.isSelect = dc.MemoTemplateUserRoleAssigns.Any(cc =>
                cc.MemoTemplateId == templateTemplateId && cc.RoleId == mr.Id);
        return Task.FromResult(r);
    }

    public Task<string> SaveTemplateRoles(int templateId, List<RoleDTO> allRole, string userId)
    {
        var dc = contextFactory.CreateDbContext();
        // first remove all role assigned on this templateId
        var trs = dc.MemoTemplateUserRoleAssigns
            .Where(cc => cc.MemoTemplateId == templateId)
            .ToList();
        dc.MemoTemplateUserRoleAssigns.RemoveRange(trs);
        dc.SaveChanges();
        foreach (var tr in allRole)
            if (tr.isSelect)
            {
                var nr = new MemoTemplateUserRoleAssign
                {
                    MemoTemplateId = templateId,
                    RoleId = tr.Id,
                    MemoTemplateAssignCreatedBy = userId,
                    MemoTemplateAssignCreatedDate = DateTime.Now,
                    MemoTemplateAssignModifiedDate = DateTime.Now,
                    MemoTemplateAssignModifiedBy = userId
                };
                dc.MemoTemplateUserRoleAssigns.Add(nr);
                dc.SaveChanges();
            }

        return Task.FromResult("OK");
    }
}
