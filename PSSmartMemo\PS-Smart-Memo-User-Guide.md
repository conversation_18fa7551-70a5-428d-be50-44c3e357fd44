# PS SMART MEMO - User Guide

## Table of Contents
1. Getting Started
2. Authentication
3. Navigation
4. Main Features
5. User Profile
6. Administrative Functions

## 1. Getting Started
PS SMART MEMO is a web-based memo management system that allows users to create, manage, and process memos within the organization.

### System Requirements
- Modern web browser (Chrome, Firefox, Edge)
- Network access to the application server
- Valid company credentials

## 2. Authentication
- Access the system using your company network credentials
- The system uses Windows Authentication (Active Directory)
- Your profile picture and name will be displayed in the top-right corner

## 3. Navigation
### Main Menu
- Use the menu icon (☰) in the top-left corner to toggle the navigation sidebar
- Search functionality available in the sidebar to quickly find menu items
- Breadcrumb navigation shows your current location in the application

### Notifications
- Bell icon in the top-right shows pending approvals
- Numbers indicate total pending items
- Click to view detailed notification list

## 4. Main Features

### Memos
- Access via "Memos" menu item
- Create new memos
- Attach files and documents
- Track memo status
- View memo history

### Worklist/Approval List
- Access via "Worklist" or "Approval List" menu
- View items requiring your approval
- Filter and sort capabilities
- Take action on pending items

### Corporate Watch List
- Monitor project-specific approvals
- Toggle between Direct and Indirect approvals
- Real-time status updates

## 5. User Profile
- Access your profile via the profile picture in top-right
- View personal information
- Update profile picture
- View assigned roles and permissions

## 6. Administrative Functions
(Only visible to users with admin rights)

### User Management
- Add/Edit/Delete users
- Assign roles
- Manage user permissions
- Upload user profile pictures

### Role Management
- Create and modify roles
- Assign permissions to roles
- Map users to roles

## Tips and Best Practices
1. Keep your profile information updated
2. Regularly check your notification center
3. Use the search function in the sidebar for quick navigation
4. Monitor your worklist for pending approvals
5. Use breadcrumbs for easy navigation

## Support
For technical support or questions, contact your system administrator.


# PS SMART MEMO - User Guide

## Table of Contents
1. Getting Started
2. Authentication
3. Navigation
4. Main Features
5. User Profile
6. Administrative Functions

## 1. Getting Started
PS SMART MEMO is a web-based memo management system that allows users to create, manage, and process memos within the organization.

### System Requirements
- Modern web browser (Chrome, Firefox, Edge)
- Network access to the application server
- Valid company credentials

## 2. Authentication
- Access the system using your company network credentials
- The system uses Windows Authentication (Active Directory)
- Your profile picture and name will be displayed in the top-right corner

## 3. Navigation
### Main Menu
- Use the menu icon (☰) in the top-left corner to toggle the navigation sidebar
- Search functionality available in the sidebar to quickly find menu items
- Breadcrumb navigation shows your current location in the application

### Notifications
- Bell icon in the top-right shows pending approvals
- Numbers indicate total pending items
- Click to view detailed notification list

## 4. Main Features

### Memos
- Access via "Memos" menu item
- Create new memos
- Attach files and documents
- Track memo status
- View memo history

### Worklist/Approval List
- Access via "Worklist" or "Approval List" menu
- View items requiring your approval
- Filter and sort capabilities
- Take action on pending items

### Corporate Watch List
- Monitor project-specific approvals
- Toggle between Direct and Indirect approvals
- Real-time status updates

## 5. User Profile
- Access your profile via the profile picture in top-right
- View personal information
- Update profile picture
- View assigned roles and permissions

## 6. Administrative Functions
(Only visible to users with admin rights)

### User Management
- Add/Edit/Delete users
- Assign roles
- Manage user permissions
- Upload user profile pictures

### Role Management
- Create and modify roles
- Assign permissions to roles
- Map users to roles

## Tips and Best Practices
1. Keep your profile information updated
2. Regularly check your notification center
3. Use the search function in the sidebar for quick navigation
4. Monitor your worklist for pending approvals
5. Use breadcrumbs for easy navigation

## Support
For technical support or questions, contact your system administrator.

# Memo Creation Process

## Overview
The memo creation process allows users to create, draft, and submit memos using predefined templates. This guide explains the step-by-step process of creating and managing memos.

## Accessing Memo Creation
1. Navigate to "Memos" in the main menu
2. Click "Create New Memo" button
3. The memo creation form will open with default values

## Step-by-Step Memo Creation

### 1. Initial Setup
- Select a memo template from the dropdown
- The system will automatically:
  - Generate a temporary memo code
  - Set the creation date
  - Identify your department and division
  - Set initial status as "DRAFT"

### 2. Basic Information
Fill in the required memo details:
- Memo Title (required)
- Memo Type (auto-populated based on template)
- Reference Number (auto-generated upon publishing)
- Department (auto-populated from user profile)
- Division (auto-populated from user profile)

### 3. Content Sections
For each template section:
- Fill in the required content using the rich text editor
- Format text as needed using the toolbar options
- Add tables, lists, or other formatting as required
- Mark sections as complete
- Preview section content before proceeding

### 4. File Attachments
Attach relevant documents:
1. Click "Add Attachment" button
2. Select file type from the dropdown
3. Browse and select file (supported formats: PDF, DOC, DOCX, XLS, XLSX, PNG, JPG, JPEG)
4. Add description for the attachment
5. Click "Upload"

Important Notes:
- Maximum file size: 30MB per file
- Ensure all attachments are relevant and properly labeled
- You can remove attachments before saving

### 5. Approver Configuration
Set up the approval workflow:
1. System automatically sets you as the initiator
2. Add required approvers based on template requirements
3. Configure for each approver:
   - Role/Position
   - User selection
   - Email address (auto-populated)
   - Approval order

### 6. Saving and Submitting

#### Save as Draft
- Click "Save as Draft" to preserve work in progress
- All sections and attachments will be saved
- Memo remains editable
- Status remains as "DRAFT"

#### Submit for Approval
1. Verify all required sections are completed
2. Ensure all necessary attachments are uploaded
3. Confirm approver list is complete
4. Click "Submit" button
5. System will:
   - Validate all required fields
   - Generate final memo code
   - Change status to "PENDING APPROVAL"
   - Notify first approver

## Managing Existing Memos

### Viewing Memos
- Access your memos from the "My Memos" list
- Memos are sorted by creation date (newest first)
- Filter memos by:
  - Status
  - Date range
  - Memo type
  - Template

### Editing Memos
- Only DRAFT memos can be edited
- Published memos cannot be modified
- To edit a draft:
  1. Select memo from list
  2. Make necessary changes
  3. Save as draft or submit

### Preview Function
- Click "Preview" button to view memo as it will appear to approvers
- Preview opens in new tab
- Shows all sections, attachments, and approver information

## Status Tracking
Monitor memo status:
- DRAFT: In progress, editable
- PENDING APPROVAL: Under review
- PUBLISHED: Approved and finalized
- REJECTED: Returned for revision

## Best Practices
1. Always save work in progress
2. Preview memo before submission
3. Verify all required sections are completed
4. Ensure approvers are correctly assigned
5. Keep attachments organized and properly labeled
6. Use clear and concise titles

## Troubleshooting
Common issues and solutions:
1. Cannot save: Check required fields
2. Upload failed: Verify file size and format
3. Submit button disabled: Ensure all required sections are complete
4. Approver not available: Contact system administrator
5. Template not visible: Check access permissions

## Important Notes
- Drafts are automatically saved every 5 minutes
- Published memos cannot be edited
- Attachments must be uploaded before final submission
- All actions are logged for audit purposes

# Memo Approval Process

## Overview
The approval process manages the workflow of memo reviews and approvals through various stakeholders. The system supports multiple approval actions including Approve, Reject, Query, and Object, with specific routing rules for each action.

## Accessing the Worklist
1. Click on "Worklist" in the main menu
2. View pending items requiring your attention
3. Items are sorted by submission date (newest first)
4. Each item shows:
   - Memo Title
   - Submission Date
   - Current Status
   - Initiator
   - Action Required

## Approval Actions

### 1. Standard Approval
When approving a memo:
1. Review memo content and attachments
2. Enter comments (if required)
3. Verify your PIN/password
4. Click "Approve" button
5. System will:
   - Record approval
   - Forward to next approver
   - Update memo status
   - Send notifications

### 2. Rejection
To reject a memo:
1. Select "Reject" option
2. Provide mandatory rejection reason
3. Verify your PIN/password
4. Click "Submit"
5. System will:
   - Return memo to initiator
   - Update status to "REJECTED"
   - Send notifications
   - Log rejection reason

### 3. Query Function
Use Query when requiring clarification:
1. Select "Query" option
2. Enter specific questions/concerns
3. Select recipient (can be previous approvers or initiator)
4. Submit query
5. System will:
   - Route memo to selected recipient
   - Put approval process on hold
   - Track query response

### 4. Object Function
For routing to specific approvers:
1. Select "Object" option
2. Choose destination approver
3. Provide reason for objection
4. Submit objection
5. System will:
   - Route to selected approver
   - Update workflow
   - Send notifications

## Approval Flow Rules

### Sequential Approval
- Approvals follow predefined sequence
- Each approver must act before memo moves to next level
- System enforces approval hierarchy

### Parallel Approval (if configured)
- Multiple approvers can review simultaneously
- All approvals required for completion
- System tracks parallel approvals

### Special Routing
1. Query Resolution:
   - Returns to original approver after response
   - Maintains approval sequence
   - Tracks query-response chain

2. Objection Handling:
   - Can route to any previous approver
   - Updates approval path
   - Maintains audit trail

## Approval Status Tracking

### Status Types
- **Pending**: Awaiting your action
- **In Progress**: Under review by others
- **Completed**: Fully approved
- **Rejected**: Returned for revision
- **Query**: Awaiting clarification
- **Objected**: Routed for specific review

### Viewing History
1. Access memo details
2. Click "View History" tab
3. See complete approval trail:
   - Approver names
   - Actions taken
   - Timestamps
   - Comments
   - Routing changes

## Notifications

### Automatic Alerts
System sends notifications for:
1. New items requiring approval
2. Queries received
3. Responses to queries
4. Final approval completion
5. Rejections

### Email Notifications
- Receive email alerts for pending actions
- Click links in email to access memo directly
- Get reminders for overdue items

## Best Practices

### For Approvers
1. Review all sections thoroughly
2. Check attachments when referenced
3. Provide clear comments
4. Use appropriate action for situation
5. Respond to queries promptly

### For Initiators
1. Monitor approval progress
2. Respond quickly to queries
3. Address rejections promptly
4. Keep stakeholders informed
5. Maintain proper documentation

## Troubleshooting

### Common Issues
1. Cannot approve:
   - Verify PIN/password
   - Check approval permissions
   - Ensure previous steps completed

2. Routing problems:
   - Verify approver availability
   - Check workflow configuration
   - Contact admin for special routing

3. Notification issues:
   - Check email settings
   - Verify contact information
   - Review spam folders

## Important Notes
- All approval actions are permanent
- Comments are mandatory for rejections
- Query responses must be complete
- System logs all actions for audit
- Approval sequence must be followed
