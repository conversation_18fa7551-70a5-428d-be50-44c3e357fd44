using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace PSSmartMemo.Services;

public class TempFileCleanupService : IHostedService, IDisposable
{
    private readonly IWebHostEnvironment _environment;
    private readonly ILogger<TempFileCleanupService> _logger;
    private Timer? _timer;

    public TempFileCleanupService(
        IWebHostEnvironment environment,
        ILogger<TempFileCleanupService> logger)
    {
        _environment = environment;
        _logger = logger;
    }

    public Task StartAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Temp File Cleanup Service is starting.");
        _timer = new Timer(DoCleanup, null, TimeSpan.Zero, TimeSpan.FromHours(1));
        return Task.CompletedTask;
    }

    private void DoCleanup(object? state)
    {
        try
        {
            var tempDir = Path.Combine(_environment.WebRootPath, "temp");
            if (!Directory.Exists(tempDir)) return;

            var files = Directory.GetFiles(tempDir, "*.pdf");
            var now = DateTime.Now;

            foreach (var file in files)
            {
                var fileInfo = new System.IO.FileInfo(file);
                if ((now - fileInfo.CreationTime).TotalHours > 1)
                {
                    try
                    {
                        File.Delete(file);
                        _logger.LogInformation($"Deleted temporary file: {file}");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"Error deleting temporary file: {file}");
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during cleanup operation");
        }
    }

    public Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Temp File Cleanup Service is stopping.");
        _timer?.Change(Timeout.Infinite, 0);
        return Task.CompletedTask;
    }

    public void Dispose()
    {
        _timer?.Dispose();
    }
}
