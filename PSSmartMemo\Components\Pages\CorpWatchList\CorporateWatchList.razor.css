/* Main container */
.corporate-approval-container {
    display: flex;
    height: calc(100vh - 85px);
    background-color: #ffffff;
    border-radius: 6px;
    overflow: auto;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
    position: relative;
    gap: 8px; /* Reduced gap between project list and content panel */
    padding-right: 12px; /* Reduced padding to the right side */
}

/* Project panel (left sidebar) */
.project-panel {
    width: 240px;
    background-color: #f8f9fa;
    display: flex;
    flex-direction: column;
    transition: all 0.3s ease;
    overflow: hidden;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    margin-top: 4px;
    margin-bottom: 4px;
    margin-left: 4px;
}

.project-panel.open {
    width: 240px;
}

.project-panel.closed {
    width: 0;
}

.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    border-bottom: 1px solid #e9ecef;
    background-color: #0078d4; /* Changed to blue corporate color */
    color: white;
}

.header-title {
    display: flex;
    align-items: center;
    font-weight: 600;
    color: white; /* Updated to match panel-header background */
    font-size: 13px;
}

.header-title .mr-2 {
    margin-right: 6px;
}

.header-actions {
    display: flex;
    align-items: center;
}

.refresh-button {
    color: white; /* Updated to match panel-header background */
}

.toggle-button {
    position: absolute;
    left: 232px; /* Adjusted for smaller panel width */
    top: 50%;
    transform: translateY(-50%);
    width: 20px;
    height: 20px;
    background: linear-gradient(135deg, #43a047 0%, #2e7d32 100%); /* Gradient green background */
    color: white;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    z-index: 10;
    box-shadow: 0 1px 4px rgba(46, 125, 50, 0.4); /* Green-tinted shadow */
    transition: all 0.3s ease;
    border: 1px solid white; /* White border for better visibility */
}

.toggle-button:hover {
    transform: translateY(-50%) scale(1.05);
    box-shadow: 0 2px 6px rgba(46, 125, 50, 0.5);
}

.project-panel.closed + .toggle-button {
    left: 8px; /* Adjusted to account for the gap */
}

/* Project list */
.project-list {
    padding: 6px;
    overflow-y: auto;
    flex: 1;
}

.project-item {
    display: flex;
    align-items: center;
    padding: 6px 8px;
    cursor: pointer;
    border-radius: 4px;
    margin-bottom: 3px;
    transition: all 0.2s ease;
    position: relative;
}

.project-item:hover {
    background-color: #e9ecef;
}

.project-item.selected {
    background-color: #B3D3EC;
    border-left: 3px solid #0078d4;
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
    transform: translateX(2px);
}

.project-item.selected .project-name {
    color: #00365a;
    font-weight: 600;
}

.project-item.selected .project-icon {
    transform: scale(1.05);
    color: white;
    background-color: #0078d4;
}

.project-icon {
    width: 22px;
    height: 22px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    margin-right: 8px;
    flex-shrink: 0;
}

.project-icon.memo {
    color: white;
    background-color: #2b88d8;
}

.project-icon.ringi {
    color: white;
    background-color: #c43e1c;
}

.project-icon.trs {
    color: white;
    background-color: #107c41;
}

.project-info {
    flex: 1;
    min-width: 0;
}

.project-name {
    font-size: 11px;
    font-weight: 500;
    color: #343a40;
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.approval-counts {
    font-size: 10px;
    color: #6c757d;
}

.count-label {
    color: #6c757d;
}

.count-value {
    font-weight: 500;
    color: #495057;
}

.ml-2 {
    margin-left: 6px;
}

.badge {
    background-color: #0078d4;
    color: white;
    min-width: 16px;
    height: 16px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    padding: 0 4px;
    font-weight: 500;
    margin-left: 6px;
}

/* Content panel (right side) */
.content-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: #ffffff;
    overflow: hidden;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    margin-top: 4px;
    margin-bottom: 4px;
}

.content-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    border-bottom: 1px solid #e9ecef;
    background-color: #e7f5e9; /* Changed to a soft green color */
    color: #2e7d32; /* Dark green text for better contrast */
}

.header-left {
    display: flex;
    align-items: center;
}

.project-title {
    display: flex;
    align-items: center;
}

.project-icon-sm {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    margin-right: 8px;
}

.project-title h2 {
    font-size: 14px;
    font-weight: 600;
    color: #2e7d32; /* Updated to match the green theme */
    margin: 0;
}

.header-right {
    display: flex;
    align-items: center;
}

/* Approval tabs */
.approval-tabs {
    flex: 1;
    overflow: hidden;
}

.approval-tabs-container {
    height: 100%;
}

::deep .mud-tabs-toolbar {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    padding: 0 16px;
    min-height: 40px;
    height: 40px;
}

::deep .mud-tab {
    text-transform: none;
    font-weight: 500;
    min-width: 120px;
    font-size: 12px;
    min-height: 40px;
    height: 40px;
}

::deep .mud-tab.mud-tab-active {
    color: #0078d4;
}

::deep .mud-tab-indicator {
    background-color: #0078d4;
    height: 2px;
}

::deep .mud-tabs-panels {
    padding: 0 !important;
    height: calc(100% - 40px);
}

/* Empty state */
.empty-state {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    background-color: #f8f9fa;
}

.empty-state-content {
    text-align: center;
    padding: 24px;
}

.empty-state-icon {
    font-size: 48px !important;
    color: #adb5bd;
    margin-bottom: 16px;
}

.empty-state-content h3 {
    font-size: 18px;
    font-weight: 500;
    color: #343a40;
    margin: 0 0 8px 0;
}

.empty-state-content p {
    font-size: 14px;
    color: #6c757d;
    margin: 0;
}

/* SfGrid customizations */
::deep .e-grid {
    border: none;
    border-radius: 0;
}

::deep .e-grid .e-gridheader {
    border-top: none;
    background-color: #f8f9fa;
}

::deep .e-grid .e-headercell {
    background-color: #f8f9fa;
    color: #495057;
    font-weight: 600;
    font-size: 10px;
    border-right: 1px solid #e9ecef;
    padding: 4px 10px;
    height: 26px;
}

::deep .e-grid .e-rowcell {
    border-right: 1px solid #e9ecef;
    padding: 3px 10px;
    font-size: 10px;
    color: #212529;
    height: 26px;
}

::deep .e-grid .e-row {
    height: 26px;
}

::deep .e-grid .e-row:hover {
    background-color: #f1f3f5;
}

::deep .e-grid .e-altrow {
    background-color: #f8f9fa;
}

::deep .e-grid .e-active {
    background-color: #e7f5ff !important;
}

/* Compact grid styles */
::deep .compact-grid .e-pager {
    padding: 2px;
    font-size: 10px;
    height: 28px;
}

::deep .compact-grid .e-pager .e-pagercontainer {
    margin: 0;
}

::deep .compact-grid .e-pager .e-pagerconstant {
    margin: 0 2px;
    font-size: 10px;
}

::deep .compact-grid .e-pager div.e-icons {
    font-size: 10px;
    width: 20px;
    height: 20px;
}

::deep .compact-grid .e-pager .e-numericitem {
    font-size: 10px;
    width: 20px;
    height: 20px;
    min-width: 20px;
}

/* Reference cell styling */
.reference-cell {
    display: flex;
    align-items: center;
    gap: 4px;
}

.reference-icon {
    margin-right: 4px;
}

.reference-link {
    color: #0078d4;
    font-weight: 500;
    text-decoration: none;
    font-size: 11px;
}

.reference-link:hover {
    text-decoration: underline;
}

/* Compact MudChip styling */
::deep .mud-chip.mud-chip-size-small {
    height: 20px;
    font-size: 10px !important;
    padding: 0 6px;
}





